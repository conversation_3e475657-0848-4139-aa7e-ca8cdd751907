<script lang="ts" setup>
const { t } = useI18n();
const store = useAppStore();
watch(
  () => store.clickNum,
  () => {
    if (store.selectIndex == "-1") {
      window.scroll({
        top: 0,
        behavior: "smooth",
      });
      return;
    }
    let scrollViews = document.querySelectorAll(".scrollViews");
    const selectIndex =
      +store.selectIndex == 2
        ? 1
        : +store.selectIndex == 1
        ? 2
        : +store.selectIndex;
    const top = (scrollViews[selectIndex] as HTMLElement).offsetTop;
    window.scroll({
      top: top,
      behavior: "smooth",
    });
  }
);
</script>
<template>
  <NuxtLayout name="default">
    <div class="home"></div>
  </NuxtLayout>
</template>
