body {
  font-weight: 400;
  font-family: HarmonyOS Sans SC-Bold;
  -webkit-font-smoothing: antialiased; /* 平滑字体，通常字体更细 */
  -moz-osx-font-smoothing: grayscale; /* 针对 macOS 的字体优化 */
  @apply font-sans text-base text-tx-primary;
}
body,
html {
  // width: 100vw;
  *:focus-visible {
    outline: none;
  }
}
.absolute-center {
  position: absolute;
  left: 50%;
  top: 50%;
  transform: translate(-50%, -50%);
}
.text-15 {
  font-size: 15px;
  @apply max-md:text-[20px];
}
.text-10 {
  font-size: 10px;
}
.text-70 {
  font-size: 70px;
  line-height: 114.9%;
  font-weight: 700;
  @apply max-2xl:text-[46px] max-xl:text-[40px] max-lg:text-[36px] max-md:text-[32px];
}
.text-78 {
  font-size: 78px;
  @apply max-2xl:text-[56px] max-xl:text-[48px] max-lg:text-[42px] max-md:text-[38px];
}
.text-50 {
  font-size: 50px;
  @apply max-2xl:text-[36px] max-md:text-[30px];
}
.form-tips {
  @apply text-tx-secondary text-xs leading-6 mt-1;
}
.el-button {
  background-color: var(--el-button-bg-color, var(--el-color-white));
}
.clearfix:after {
  content: '';
  display: block;
  clear: both;
  visibility: hidden;
}

.flex-center{
  @apply flex justify-center items-center;
}

.render-html {
  ul {
    list-style: disc;
  }
  ol {
    list-style: decimal;
  }
  h1 {
    font-size: 2em;
  }
  h2 {
    font-size: 1.5em;
  }
  h3 {
    font-size: 1.17em;
  }
  h4 {
    font-size: 1em;
  }
  h5 {
    font-size: 0.83em;
  }
  h1,
  h2,
  h3,
  h4,
  h5 {
    font-weight: bold;
  }
}

/* NProgress */
#nprogress .bar {
  background-color: var(--el-color-primary) !important;
}

video {
  -webkit-mask-image: -webkit-radial-gradient(white, black);
  -webkit-backface-visibility: hidden;
  -moz-backface-visibility: hidden;
  isolation: isolate;
}

@supports (-webkit-font-smoothing: antialiased) {
  body {
    font-weight: 400; /* 强制权重 */
    -webkit-font-smoothing: antialiased; /* 取消默认加粗 */
  }
}
@media (-webkit-min-device-pixel-ratio: 2) {
  body {
    -webkit-font-smoothing: antialiased;
  }
}
